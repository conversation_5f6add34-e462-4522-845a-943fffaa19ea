Running customer-api...
{"level":"info","msg":"Starting Customer API","time":"2025-06-03T23:39:37+07:00"}
{"level":"info","msg":"Using DATABASE_URL for database connection","time":"2025-06-03T23:39:37+07:00"}
{"level":"info","msg":"Database connection established","time":"2025-06-03T23:39:37+07:00"}
[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /health                   --> customer-backend/internal/api/routes.SetupRoutes.func1 (6 handlers)
[GIN-debug] GET    /api/v1/shops             --> customer-backend/internal/api/handlers.(*ShopHandler).GetShops-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/search      --> customer-backend/internal/api/handlers.(*ShopHandler).SearchShops-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/popular     --> customer-backend/internal/api/handlers.(*ShopHandler).GetPopularShops-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/nearby      --> customer-backend/internal/api/handlers.(*ShopHandler).GetNearbyShops-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/category/:category --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopsByCategory-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/:id         --> customer-backend/internal/api/handlers.(*ShopHandler).GetShop-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/:id/status  --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopStatus-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/:id/menu    --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItems-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/:id/menu/popular --> customer-backend/internal/api/handlers.(*MenuHandler).GetPopularItems-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/:id/menu/new --> customer-backend/internal/api/handlers.(*MenuHandler).GetNewItems-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/:id/menu/vegetarian --> customer-backend/internal/api/handlers.(*MenuHandler).GetVegetarianItems-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/:id/menu/search --> customer-backend/internal/api/handlers.(*MenuHandler).SearchMenuItems-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/:id/menu/categories --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuCategories-fm (6 handlers)
[GIN-debug] GET    /api/v1/shops/:id/menu/categories/:categoryId --> customer-backend/internal/api/handlers.(*MenuHandler).GetItemsByCategory-fm (6 handlers)
[GIN-debug] GET    /api/v1/menu/items/:itemId --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItem-fm (6 handlers)
[GIN-debug] GET    /docs/*any                --> customer-backend/internal/api/routes.SetupRoutes.func2 (6 handlers)
{"level":"info","msg":"Customer API starting on port 8900","time":"2025-06-03T23:39:37+07:00"}
{"level":"info","msg":"Getting shops with filters: page=1, limit=20","time":"2025-06-03T23:39:45+07:00"}
{"level":"info","msg":"::1 - [Tue, 03 Jun 2025 23:39:46 +07] \"GET /api/v1/shops HTTP/1.1 200 140.736042ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36\" \"\n","time":"2025-06-03T23:39:46+07:00"}
{"level":"info","msg":"Shutting down server...","time":"2025-06-03T23:39:55+07:00"}
{"level":"info","msg":"Customer API exited","time":"2025-06-03T23:39:55+07:00"}
make[3]: *** [run] Error 1
