package services

import (
	"context"
	"customer-backend/internal/repositories"
	"customer-backend/internal/types"
	"customer-backend/pkg/logger"
	"customer-backend/pkg/pagination"
	"fmt"

	"github.com/google/uuid"
)

// ShopService handles business logic for shop operations
type ShopService struct {
	shopRepo *repositories.ShopRepository
	logger   *logger.Logger
}

// NewShopService creates a new shop service
func NewShopService(shopRepo *repositories.ShopRepository, logger *logger.Logger) *ShopService {
	return &ShopService{
		shopRepo: shopRepo,
		logger:   logger,
	}
}

// GetShopSettings retrieves shop settings for customer view
func (s *ShopService) GetShopSettings(ctx context.Context, shopID uuid.UUID) (*types.CustomerShopSettings, error) {
	s.logger.Infof("Getting shop settings for shop ID: %s", shopID.String())

	shop, err := s.shopRepo.GetShopSettings(ctx, shopID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get shop settings")
		return nil, fmt.Errorf("failed to get shop settings: %w", err)
	}

	// Add any business logic transformations here
	s.enrichShopSettings(shop)

	return shop, nil
}

// GetShops retrieves shops based on customer filters with pagination
func (s *ShopService) GetShops(ctx context.Context, filters types.ShopFilters) (*types.ShopListResponse, error) {
	s.logger.Infof("Getting shops with filters: page=%d, limit=%d", filters.Page, filters.Limit)

	// Apply defaults
	filters.CustomerPagination.ApplyDefaults()
	filters.CustomerSorting.ApplyDefaults()

	// Validate and adjust limits
	if filters.Limit > 100 {
		filters.Limit = 100
	}

	shops, total, err := s.shopRepo.GetShopsByFilters(ctx, filters)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get shops")
		return nil, fmt.Errorf("failed to get shops: %w", err)
	}

	// Create pagination info with center pagination
	paginationConfig := pagination.DefaultCenterConfig()
	paginationInfo := pagination.CreatePaginationInfo(total, filters.Page, filters.Limit, paginationConfig)

	// Create response
	response := &types.ShopListResponse{
		CustomerResponse: types.CreateCustomerResponse(
			nil, // Don't set data here since ShopListResponse has its own Data field
			"Shops retrieved successfully",
			paginationInfo,
		),
	}

	// Set the shops data in the specific Data field
	response.Data.Shops = shops

	return response, nil
}

// GetNearbyShops retrieves shops near a specific location
func (s *ShopService) GetNearbyShops(ctx context.Context, latitude, longitude, radius float64, filters types.ShopFilters) (*types.ShopListResponse, error) {
	s.logger.Infof("Getting nearby shops: lat=%f, lng=%f, radius=%f", latitude, longitude, radius)

	// Set location filters
	filters.Latitude = &latitude
	filters.Longitude = &longitude
	filters.Radius = &radius

	// Default sorting for nearby shops
	if filters.SortBy == "" {
		filters.SortBy = "distance"
	}

	return s.GetShops(ctx, filters)
}

// SearchShops searches for shops based on query string
func (s *ShopService) SearchShops(ctx context.Context, query string, filters types.ShopFilters) (*types.ShopListResponse, error) {
	s.logger.Infof("Searching shops with query: %s", query)

	// Set search filter
	filters.Search = query

	// Default sorting for search results
	if filters.SortBy == "" {
		filters.SortBy = "rating"
	}

	return s.GetShops(ctx, filters)
}

// GetPopularShops retrieves popular shops
func (s *ShopService) GetPopularShops(ctx context.Context, filters types.ShopFilters) (*types.ShopListResponse, error) {
	s.logger.Info("Getting popular shops")

	// Set filters for popular shops
	minRating := 4.0
	filters.MinRating = &minRating
	filters.SortBy = "rating"
	filters.SortOrder = "desc"

	return s.GetShops(ctx, filters)
}

// GetShopsByCategory retrieves shops by cuisine category
func (s *ShopService) GetShopsByCategory(ctx context.Context, category string, filters types.ShopFilters) (*types.ShopListResponse, error) {
	s.logger.Infof("Getting shops by category: %s", category)

	// Set category filter
	filters.CuisineType = []string{category}

	return s.GetShops(ctx, filters)
}

// ValidateShopAccess checks if a shop is accessible to customers
func (s *ShopService) ValidateShopAccess(ctx context.Context, shopID uuid.UUID) error {
	shop, err := s.shopRepo.GetShopSettings(ctx, shopID)
	if err != nil {
		return fmt.Errorf("shop not found or not accessible: %w", err)
	}

	if !shop.IsActive {
		return fmt.Errorf("shop is not active")
	}

	// Add any additional access validation logic here
	// For example, check if shop is temporarily closed, etc.

	return nil
}

// GetShopOperatingStatus checks if a shop is currently open
func (s *ShopService) GetShopOperatingStatus(ctx context.Context, shopID uuid.UUID) (bool, error) {
	shop, err := s.shopRepo.GetShopSettings(ctx, shopID)
	if err != nil {
		return false, err
	}

	return shop.IsOpen, nil
}

// Helper methods

// enrichShopSettings adds additional computed fields to shop settings
func (s *ShopService) enrichShopSettings(shop *types.CustomerShopSettings) {
	// Add computed fields or business logic transformations

	// Example: Calculate distance if user location is available
	// This would require user context

	// Example: Add promotional information
	// shop.HasActivePromotions = s.checkActivePromotions(shop.ID)

	// Example: Add estimated delivery time
	// shop.EstimatedDeliveryTime = s.calculateDeliveryTime(shop.ID)

	// For now, just ensure all required fields are set
	if shop.Currency == "" {
		shop.Currency = "USD"
	}

	if shop.Theme.PrimaryColor == "" {
		shop.Theme.PrimaryColor = "#3B82F6"
	}
}

// GetShopFilterOptions retrieves available filter options for shops
func (s *ShopService) GetShopFilterOptions(ctx context.Context) (*types.ShopFilterOptions, error) {
	// This would typically query the database for available options
	// For now, return static options

	return &types.ShopFilterOptions{
		CuisineTypes: []string{
			"Italian", "Chinese", "Japanese", "Mexican", "Indian",
			"Thai", "American", "French", "Mediterranean", "Korean",
		},
		PriceRanges: []string{"$", "$$", "$$$", "$$$$"},
		Features: []string{
			"delivery", "pickup", "reservations", "online_ordering",
			"loyalty_program", "gift_cards",
		},
	}, nil
}
