"use client";

import { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { CenterPagination, PaginationInfo } from "@/components/ui/pagination";
import { Search, MapPin, Star, ChevronDown, Navigation } from "lucide-react";
import Header from "@/components/Header";
import { ShopFilters } from "@/lib/services/customerApiClient";
import { useFilteredPagination } from "@/lib/hooks/usePagination";
import { useGetShopsQuery } from "@/lib/store/api/customerApi";
import GoogleMapComponent from "@/components/GoogleMapReact";
import type { ShopLocation } from "@/components/GoogleMapReact";
import Link from "next/link";

// Sample restaurant data for demonstration (Bangkok area)
const sampleRestaurants: ShopLocation[] = [
  {
    id: "sample-1",
    name: "Bangkok Noodle House",
    description: "Authentic Thai noodles and street food",
    lat: 13.7563,
    lng: 100.5018,
    address: "123 Sukhumvit Road, Bangkok",
    rating: 4.5,
    priceRange: "$$",
    cuisineType: "Thai",
    isOpen: true,
    image: "/api/placeholder/100/100",
    phone: "+66 2 123 4567",
    website: "https://example.com",
  },
  {
    id: "sample-2",
    name: "Siam Fusion Restaurant",
    description: "Modern Thai cuisine with international influences",
    lat: 13.7440,
    lng: 100.5255,
    address: "456 Silom Road, Bangkok",
    rating: 4.8,
    priceRange: "$$$",
    cuisineType: "Fusion",
    isOpen: true,
    image: "/api/placeholder/100/100",
    phone: "+66 2 234 5678",
    website: "https://example.com",
  },
  {
    id: "sample-3",
    name: "Street Food Paradise",
    description: "Traditional Thai street food experience",
    lat: 13.7650,
    lng: 100.5380,
    address: "789 Chatuchak Market, Bangkok",
    rating: 4.2,
    priceRange: "$",
    cuisineType: "Street Food",
    isOpen: false,
    image: "/api/placeholder/100/100",
    phone: "+66 2 345 6789",
    website: "https://example.com",
  },
  {
    id: "sample-4",
    name: "Royal Thai Palace",
    description: "Upscale Thai dining with royal recipes",
    lat: 13.7500,
    lng: 100.4920,
    address: "321 Ratchadamri Road, Bangkok",
    rating: 4.9,
    priceRange: "$$$$",
    cuisineType: "Thai",
    isOpen: true,
    image: "/api/placeholder/100/100",
    phone: "+66 2 456 7890",
    website: "https://example.com",
  },
];

export default function HomePage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [locationQuery, setLocationQuery] = useState("");
  const [selectedCuisine] = useState<string>("all");
  const [selectedPriceRange] = useState<string>("all");
  const [selectedRating] = useState<string>("all");
  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null);
  const [mapCenter, setMapCenter] = useState<{ lat: number; lng: number } | null>(null);

  // Initialize pagination with filters
  const pagination = useFilteredPagination<ShopFilters>({
    initialPage: 1,
    initialLimit: 20,
    initialFilters: {
      search: "",
      cuisine_type: undefined,
      price_range: undefined,
      min_rating: undefined,
    },
  });

  // Prepare filters for RTK Query
  const filters: ShopFilters = {
    ...pagination.filters,
    page: pagination.currentPage,
    limit: pagination.limit,
    search: searchQuery || undefined,
    cuisine_type: selectedCuisine !== "all" ? [selectedCuisine] : undefined,
    price_range: selectedPriceRange !== "all" ? [selectedPriceRange] : undefined,
    min_rating: selectedRating !== "all" ? parseFloat(selectedRating) : undefined,
  };

  // Use RTK Query to fetch shops
  const {
    data: shopsResponse,
    error,
    isLoading,
    refetch,
  } = useGetShopsQuery(filters);

  // Extract data from response
  const shops = useMemo(() => shopsResponse?.data?.shops || [], [shopsResponse?.data?.shops]);

  // Convert shops to map locations, fallback to sample data for demonstration
  const shopLocations: ShopLocation[] = useMemo(() => {
    const apiShops = shops
      .filter(shop => shop.address?.latitude && shop.address?.longitude)
      .map(shop => ({
        id: shop.id,
        name: shop.name,
        description: shop.description,
        lat: shop.address.latitude!,
        lng: shop.address.longitude!,
        address: `${shop.address.street}, ${shop.address.city}`,
        rating: shop.rating,
        priceRange: shop.price_range,
        cuisineType: shop.cuisine_type,
        isOpen: shop.is_open,
        image: shop.logo,
        phone: shop.phone,
        website: shop.website,
      }));

    // If no API shops with coordinates, use sample data for demonstration
    return apiShops.length > 0 ? apiShops : sampleRestaurants;
  }, [shops]);

  // Update pagination info when data changes
  useEffect(() => {
    if (shopsResponse?.pagination) {
      pagination.setPaginationInfo(shopsResponse.pagination);
    }
  }, [shopsResponse?.pagination, pagination]);

  // Get user location on component mount
  useEffect(() => {
    const getUserLocation = async () => {
      try {
        const position = await new Promise<GeolocationPosition>((resolve, reject) => {
          navigator.geolocation.getCurrentPosition(resolve, reject);
        });
        const location = {
          lat: position.coords.latitude,
          lng: position.coords.longitude,
        };
        setUserLocation(location);
        setMapCenter(location);
      } catch (error) {
        console.warn('Could not get user location:', error);
        // Default to Bangkok, Thailand
        const defaultLocation = { lat: 13.7563, lng: 100.5018 };
        setMapCenter(defaultLocation);
      }
    };

    getUserLocation();
  }, []);

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    pagination.setPage(1);
  };

  // Handle location search
  const handleLocationSearch = (query: string) => {
    setLocationQuery(query);
    // TODO: Implement geocoding to convert address to coordinates
  };

  // Handle shop selection from map
  const handleShopClick = (shop: ShopLocation) => {
    // Navigate to shop detail page
    window.location.href = `/food/${shop.id}`;
  };

  // Handle map click for location selection
  const handleMapClick = (lat: number, lng: number) => {
    setMapCenter({ lat, lng });
    // TODO: Implement reverse geocoding to get address
  };

  // Handle "Find My Location" button
  const handleFindMyLocation = async () => {
    try {
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject);
      });
      const location = {
        lat: position.coords.latitude,
        lng: position.coords.longitude,
      };
      setUserLocation(location);
      setMapCenter(location);
    } catch (error) {
      console.error('Could not get user location:', error);
      alert('Unable to get your location. Please check your browser permissions.');
    }
  };

  // TODO: Add filter handlers when implementing dropdown functionality

  if (isLoading && shops.length === 0) {
    return (
      <div className="relative flex size-full min-h-screen flex-col bg-background">
        <div className="layout-container flex h-full grow flex-col">
          <Header />
          <div className="flex flex-1 justify-center items-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading restaurants...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    const errorMessage = 'status' in error
      ? `Error ${error.status}: Failed to load restaurants`
      : error.message || 'Failed to load restaurants';

    return (
      <div className="relative flex size-full min-h-screen flex-col bg-background">
        <div className="layout-container flex h-full grow flex-col">
          <Header />
          <div className="flex flex-1 justify-center items-center">
            <div className="text-center">
              <p className="text-destructive mb-4">{errorMessage}</p>
              <Button onClick={() => refetch()}>Try Again</Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-background">
      <div className="layout-container flex h-full grow flex-col">
        <Header />

        {/* Main Content */}
        <div className="px-40 flex flex-1 justify-center py-5">
          <div className="layout-content-container flex flex-col max-w-[960px] flex-1">

            {/* Hero Section with Search */}
            <div className="px-4 py-8">
              <h1 className="text-foreground tracking-light text-[32px] font-bold leading-tight text-center mb-8">
                Discover Restaurants Near You
              </h1>

              {/* Search Bars */}
              <div className="space-y-4 mb-6">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Search for restaurants or cuisines"
                    value={searchQuery}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-10 h-12"
                  />
                </div>

                <div className="relative">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Enter an address"
                    value={locationQuery}
                    onChange={(e) => handleLocationSearch(e.target.value)}
                    className="pl-10 h-12"
                  />
                </div>
              </div>

              {/* Interactive Google Map */}
              <div className="w-full h-64 rounded-lg mb-6 relative overflow-hidden">
                <GoogleMapComponent
                  shops={shopLocations}
                  center={mapCenter || undefined}
                  zoom={15}
                  height="256px"
                  width="100%"
                  showUserLocation={true}
                  onShopClick={handleShopClick}
                  onMapClick={handleMapClick}
                  className="rounded-lg"
                />
                {/* Find My Location Button */}
                <div className="absolute top-4 right-4">
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={handleFindMyLocation}
                    className="bg-white/90 hover:bg-white shadow-md"
                    title="Find my location"
                  >
                    <Navigation className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            <h2 className="text-foreground tracking-light text-[28px] font-bold leading-tight px-4 text-left pb-3">
              Restaurants Near You
            </h2>

            {/* Filter Dropdowns */}
            <div className="flex gap-3 p-4 flex-wrap">
              <div className="relative">
                <Button variant="outline" className="h-10 px-4">
                  Cuisine <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </div>
              <div className="relative">
                <Button variant="outline" className="h-10 px-4">
                  Price Range <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </div>
              <div className="relative">
                <Button variant="outline" className="h-10 px-4">
                  Ratings <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Pagination Info */}
            <div className="px-4 pb-3">
              <PaginationInfo
                startItem={pagination.startItem}
                endItem={pagination.endItem}
                totalItems={pagination.totalItems}
              />
            </div>

            {/* Restaurant List */}
            {shopLocations.map((shop) => (
              <div key={shop.id} className="p-4">
                <Link href={`/food/${shop.id}`}>
                  <Card className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                    <div className="flex items-center gap-4 p-4">
                      <div
                        className="w-16 h-16 bg-center bg-no-repeat bg-cover rounded-lg flex-shrink-0"
                        style={{
                          backgroundImage: shop.image ? `url("${shop.image}")` : 'none',
                          backgroundColor: shop.image ? 'transparent' : '#e5ccb2'
                        }}
                      />
                      <div className="flex-1 min-w-0">
                        <CardHeader className="p-0">
                          <CardTitle className="text-lg font-semibold text-foreground">
                            {shop.name}
                          </CardTitle>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                              <span>{shop.rating || "4.5"}</span>
                            </div>
                            <span>•</span>
                            <span>{shop.cuisineType || "Italian"}</span>
                            <span>•</span>
                            <span>{shop.priceRange || "$$"}</span>
                          </div>
                          {shop.description && (
                            <CardDescription className="text-sm mt-1">
                              {shop.description}
                            </CardDescription>
                          )}
                        </CardHeader>
                      </div>
                    </div>
                  </Card>
                </Link>
              </div>
            ))}

            {/* Center Pagination */}
            {pagination.totalPages > 1 && (
              <div className="px-4 py-6">
                <CenterPagination
                  currentPage={pagination.currentPage}
                  totalPages={pagination.totalPages}
                  centerPages={pagination.centerPages}
                  showFirst={pagination.showFirst}
                  showLast={pagination.showLast}
                  onPageChange={pagination.setPage}
                  canGoPrev={pagination.canGoPrev}
                  canGoNext={pagination.canGoNext}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
